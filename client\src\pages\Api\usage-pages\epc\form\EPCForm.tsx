import React, { FormEvent, useState, useCallback } from "react";
import { useAppDispatch, useAppSelector } from "../../../../../redux/hooks";
import { postEPC } from "../../../../../redux/actions/epcActions";
import PDFUpload from "../../../../../components/PDFUpload/PDFUpload";
import { PDFOptions } from "../types";
import { Redirect } from "react-router-dom";

const EPCForm = () => {
  const [pdfOptions, setPdfOptions] = useState<PDFOptions>({
    file: undefined,
    preview: undefined,
  });

  const dispatch = useAppDispatch();
  const { loading } = useAppSelector((state) => state.epc);
  const apiKey = useAppSelector((state) => state.auth.user?.api_key);

  const handleFileSelected = useCallback((file: File) => {
    setPdfOptions({
      file,
      preview: file.name,
    });
  }, []);

  const handleFormSubmit = useCallback((e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!pdfOptions.file) {
      return;
    }

    const formData = {
      file: pdfOptions.file,
      preview: pdfOptions.preview,
    };

    if (apiKey) {
      dispatch(
        postEPC({
          formData,
          apiKey,
          contentType: "multipart/form-data",
        })
      );
    }
  }, [pdfOptions, apiKey, dispatch]);

  if (loading) {
    return <Redirect to="/epc-result" />;
  }

  return (
    <div className="api-form">
      <div className="form-header">
        <h2>EPC API - PDF Verwerking</h2>
        <p>
          Upload een EPC certificaat (NTA 8800 rapport) om gestructureerde data te extraheren.
          Deze service is gratis en helpt bij het genereren van verduurzamingsadvies.
        </p>
      </div>

      <form className="form" onSubmit={handleFormSubmit}>
        <div className="form-required-fields">
          <PDFUpload
            onFileSelected={handleFileSelected}
            selectedFile={pdfOptions.file}
          />
        </div>

        <div className="form-submit">
          {pdfOptions.file && (
            <button type="submit" className="btn btn-primary" disabled={loading}>
              {loading ? "Verwerken..." : "PDF Verwerken"}
            </button>
          )}
        </div>
      </form>

      <div className="api-info">
        <h3>Over de EPC API</h3>
        <ul>
          <li>Verwerkt NTA 8800 rapporten (EPC certificaten)</li>
          <li>Extraheert gestructureerde data voor verduurzamingsadvies</li>
          <li>Gratis service - geen kosten voor gebruik</li>
          <li>Ondersteunt PDF bestanden tot 10MB</li>
          <li>Gebruikt OCR technologie voor data-extractie</li>
        </ul>
      </div>
    </div>
  );
};

export default EPCForm;
