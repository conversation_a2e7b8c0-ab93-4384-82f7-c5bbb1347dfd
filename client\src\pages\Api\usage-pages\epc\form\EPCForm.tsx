import React, { FormEvent, useState, useCallback } from "react";
import { useAppDispatch, useAppSelector } from "../../../../../redux/hooks";
import { postEPC } from "../../../../../redux/actions/epcActions";
import PDFUpload from "../../../../../components/PDFUpload/PDFUpload";
import { PDFOptions } from "../types";
import { Redirect } from "react-router-dom";

const EPCForm = () => {
  const [pdfOptions, setPdfOptions] = useState<PDFOptions>({
    file: undefined,
    preview: undefined,
  });

  const dispatch = useAppDispatch();
  const { loading } = useAppSelector((state) => state.epc);
  const apiKey = useAppSelector((state) => state.auth.user?.api_key);

  const handleFileSelected = useCallback((file: File) => {
    setPdfOptions({
      file,
      preview: file.name,
    });
  }, []);

  const handleFormSubmit = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();

      if (!pdfOptions.file) {
        return;
      }

      const formData = {
        file: pdfOptions.file,
        preview: pdfOptions.preview,
      };

      if (apiKey) {
        dispatch(
          postEPC({
            formData,
            apiKey,
            contentType: "multipart/form-data",
          }),
        );
      }
    },
    [pdfOptions, apiKey, dispatch],
  );

  if (loading) {
    return <Redirect to="/epc-result" />;
  }

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">
          EPC API - PDF Verwerking
        </h2>
        <p className="text-gray-600 leading-relaxed">
          Upload een EPC certificaat (NTA 8800 rapport) om gestructureerde data
          te extraheren. Deze service is gratis en helpt bij het genereren van
          verduurzamingsadvies.
        </p>
      </div>

      <form className="space-y-6" onSubmit={handleFormSubmit}>
        <div>
          <PDFUpload
            onFileSelected={handleFileSelected}
            selectedFile={pdfOptions.file}
          />
        </div>

        <div className="flex justify-center">
          {pdfOptions.file && (
            <button
              type="submit"
              className="bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={loading}
            >
              {loading ? "Verwerken..." : "PDF Verwerken"}
            </button>
          )}
        </div>
      </form>

      <div className="mt-8 p-6 bg-gray-50 rounded-lg border-l-4 border-primary">
        <h3 className="text-lg font-semibold text-primary mb-4">
          Over de EPC API
        </h3>
        <ul className="space-y-2 text-gray-700">
          <li className="flex items-start">
            <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
            Verwerkt NTA 8800 rapporten (EPC certificaten)
          </li>
          <li className="flex items-start">
            <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
            Extraheert gestructureerde data voor verduurzamingsadvies
          </li>
          <li className="flex items-start">
            <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
            Gratis service - geen kosten voor gebruik
          </li>
          <li className="flex items-start">
            <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
            Ondersteunt PDF bestanden tot 10MB
          </li>
          <li className="flex items-start">
            <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
            Gebruikt OCR technologie voor data-extractie
          </li>
        </ul>
      </div>
    </div>
  );
};

export default EPCForm;
