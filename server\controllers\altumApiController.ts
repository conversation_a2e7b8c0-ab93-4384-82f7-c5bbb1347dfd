import axios from "axios";
import bcrypt from "bcryptjs";
import { CookieOptions, NextFunction, Request, Response } from "express";
import { Config } from "../@types";
import { getEsOutput } from "../elasticSearch";
import AppError from "../utils/appError";
import pool from "../db";
import { createAnalyticsTag } from "../utils/sendAnalytics";

export const altumApiCall = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  const data = req.body;
  const config: Config = {
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
      "x-api-key": data.apiKey,
    },
  };
  axios.defaults.withCredentials = false;

  try {
    // First check if this API key has made any calls before
    const previousCalls = await pool.query(
      `SELECT COUNT(*) 
       FROM analytics_logger 
       WHERE category = 'API' 
       AND event = 'ApiCall'`,
    );

    // Make the API call
    const response = await axios.post(
      `https://api.altum.ai/${data.apiName}`,
      data.formData,
      config,
    );

    // If this is their first call, log it as FirstApiCall
    if (parseInt(previousCalls.rows[0].count) === 0) {
      await createAnalyticsTag({
        event: "FirstApiCall",
        category: "API",
      });
    }

    // Always log the regular API call
    await createAnalyticsTag({
      event: "ApiCall",
      category: "API",
    });

    res.status(200).json({
      status: "success",
      data: response.data,
    });
  } catch (error: any) {
    if (error.response) {
      res.status(error.response.status).json({
        status: "fail",
        message: error.response.data,
      });
    } else {
      res.status(400).json({
        status: "fail",
        error,
      });
    }
  }
};

export const autoSuggestCall = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  const data = req.body;
  const config: Config = {
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
      "x-api-key": data.apiKey,
    },
    
  };
  axios.defaults.withCredentials = false;

  try {
    const response = await axios.get(
      `https://api.altum.ai/${data.apiName}/v2?search=${data.formData.search}${
        data.formData.fuzzy ? "&fuzzy=" + data.formData.fuzzy : ""
      }${!!data.formData.limit ? "&limit=" + data.formData.limit : ""}`,
      config,
    );
    res.status(200).json({
      status: "success",
      data: response.data,
    });
  } catch (error: any) {
    if (error.response) {
      res.status(error.response.status).json({
        status: "fail",
        message: error.response.data,
      });
    } else {
      res.status(400).json({
        status: "fail",
        error,
      });
    }
  }
};

export const getAddressData = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const address = req.body.house_address;
    const response = (await getEsOutput(address)) as any;
    if (!response || !response.location) {
      throw new AppError("Invalid address data received", 400);
    }

    const {
      location: { lat, lon },
      streetname,
      housenumber,
      city,
    } = response;
    const houseAddress = `${streetname} ${housenumber} ${city ?? ""}`;
    const imageBuffer = await getStreetViewImage(lat, lon);
    const mapBuffer = await getMapUrl(lon, lat);

    res.status(200).json({
      status: "success",
      data: {
        imageBuffer,
        mapBuffer,
        houseAddress,
      },
    });
  } catch (error: any) {
    next(new AppError(error.message, error.statusCode || 400));
  }
};

export async function getStreetViewImage(
  latitude: number,
  longitude: number,
): Promise<Buffer> {
  const apiKey = process.env.GOOGLE_STREETVIEW_APIKEY;
  if (!apiKey) {
    throw new AppError("Google Street View API key is missing", 500);
  }

  try {
    const response = await axios.get(
      `https://maps.googleapis.com/maps/api/streetview?size=600x200&location=${latitude},${longitude}&key=${apiKey}`,
      { responseType: "arraybuffer" },
    );
    return Buffer.from(response.data, "binary");
  } catch (error: any) {
    console.log(error);
    console.error("Error fetching Street View image:", error.message);
    throw new AppError("Failed to fetch Street View image", 500);
  }
}

export const getMapUrl = async (lon: number, lat: number): Promise<Buffer> => {
  const apiKey = process.env.GOOGLE_STREETVIEW_APIKEY;
  if (!apiKey) {
    throw new AppError("Google Maps API key is missing", 500);
  }
  try {
    const response = await axios.get(
      `https://maps.googleapis.com/maps/api/staticmap?key=${apiKey}&center=${lat},${lon}&markers=color:red%7Clabel:S%7C${lat},${lon}&zoom=14&size=800x200&language=nl`,
      { responseType: "arraybuffer" },
    );
    return Buffer.from(response.data, "binary");
  } catch (error: any) {
    console.error("Error fetching map:", error);
    console.log(error);
    console.error("Error fetching map:", error.message);
    throw new AppError("Failed to fetch map", 500);
  }
};
