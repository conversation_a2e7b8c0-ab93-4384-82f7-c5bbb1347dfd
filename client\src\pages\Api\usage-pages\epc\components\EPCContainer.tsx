import React, { ReactNode } from "react";
import ApiFormContainer from "../../components/ApiFormContainer";
import { EnergyLabel } from "../../../../../assets/images/api/APIimages";

const progress = ["Upload PDF", "Verwerk Data", "Bekijk Resultaten"];

const EPCContainer: React.FC<{
  children: ReactNode;
  page: number;
}> = ({ children, page }) => {
  return (
    <ApiFormContainer
      page={page}
      title="EPC API"
      subtitle="Upload een EPC certificaat (NTA 8800 rapport) om gestructureerde data te extraheren voor verduurzamingsadvies. Deze gratis service gebruikt OCR technologie om PDF's te verwerken."
      progress={progress}
      resultSelector={(state) => state.epc.result}
      link="https://docs.altum.ai/english/apis/epc-api"
      image={EnergyLabel}
    >
      {children}
    </ApiFormContainer>
  );
};

export default EPCContainer;
