import express from "express";
import { protect } from "../controllers/authController";
import {
  sendSupportEmail,
  sendCoropData,
  sendGementeeData,
  tagAmplitudeEvent,
  translator,
} from "../controllers/mopsusController";
import {
  createSubscription,
  cancelSubscription,
  updateSubscription,
  checkSubscriptionStatus,
  updatePaymentDetails,
  setDefaultPaymentMethod,
  createTransactionSubscription,
  cancelTransactionSubscription,
  updateTransactionSubscription,
  checkTransactionSubscriptionStatus,
  checkWozSubscriptionStatus,
  checkObjectDataSubscriptionStatus,
  checkAVMSubscriptionStatus,
  checkReferenceSubscriptionStatus,
  checkECOSubscriptionStatus,
  checkEnergySubscriptionStatus,
  removePaymentDetails,
} from "../controllers/stripeController";
import { createSetupIntent, createPaymentIntent } from "../utils/stripeHelper";
import {
  altumApiCall,
  autoSuggestCall,
  getAddressData,
} from "../controllers/altumApiController";

const router = express.Router();
exports.router = router;

router.post("/send-support-email", protect, sendSupportEmail);
// router.get(
// 	"/auth-stripe-user",
// 	protect,
// 	billflowController.hashAuthentication
// );
router.get("/get-corop", sendCoropData);
router.get("/get-gementee", sendGementeeData);

router.post("/create-subscription", protect, createSubscription);
router.post("/cancel-subscription", protect, cancelSubscription);
router.post("/update-subscription", protect, updateSubscription);
router.get("/check-subscription-status", protect, checkSubscriptionStatus);
router.post("/update-payment-details", protect, updatePaymentDetails);
router.post("/remove-payment-details", protect, removePaymentDetails);
router.post("/set-default-payment-method", protect, setDefaultPaymentMethod);

router.get("/create-setup-intent", protect, createSetupIntent);
router.get("/create-payment-intent", protect, createPaymentIntent);
// Transaction Routes
router.post(
  "/create-transaction-subscription",
  protect,
  createTransactionSubscription,
);
router.post(
  "/cancel-transaction-subscription",
  protect,
  cancelTransactionSubscription,
);

router.post(
  "/update-transaction-subscription",
  protect,
  updateTransactionSubscription,
);
router.get(
  "/check-transaction-subscription-status",
  protect,
  checkTransactionSubscriptionStatus,
);
router.get(
  "/check-woz-subscription-status",
  protect,
  checkWozSubscriptionStatus,
);

router.get(
  "/check-object-data-subscription-status",
  protect,
  checkObjectDataSubscriptionStatus,
);
router.get(
  "/check-reference-subscription-status",
  protect,
  checkReferenceSubscriptionStatus,
);
router.get(
  "/check-avm-subscription-status",
  protect,
  checkAVMSubscriptionStatus,
);

router.get(
  "/check-sustainability-subscription-status",
  protect,
  checkECOSubscriptionStatus,
);
router.get(
  "/check-energy-label-subscription-status",
  protect,
  checkEnergySubscriptionStatus,
);
router.post("/tag-event", tagAmplitudeEvent);
router.post("/translator", translator);
router.post("/api-call", protect, altumApiCall);
router.post("/autosuggest", protect, autoSuggestCall);
router.post("/address-data", protect, getAddressData);

export default router;
