import axios from "axios";
import Stripe from "stripe";
import pool from "../db";
import AppError from "./appError";
import Email from "./emailHandler";
import {
  createUsage,
  deleteSubscription,
} from "../controllers/stripeController";
import fetchApiUsage from "./fetchApiUsage";
import logger from "./logger";
import {
  SENDGRID_429_ID,
  SENDGRID_403_ID,
  MOPSUS_100,
  MOPSUS_250,
  MOPSUS_750,
  MOPSUS_2000,
  TRANSACTION_100,
  MOPSUS_3000,
  MOPSUS_9000,
  MOPSUS_24000,
  SENDGRID_422_ID,
  SENDGRID_500_ID,
} from "./constants";
import fetchApiKeyByStatusCode from "./fetchApiKeyByStatusCode";

import {
  getProduct,
  getSubscription,
  incomingInvocice,
} from "../controllers/customerPortalController";
import { User, UserNotification, UserSubscription } from "../@types";
import { fetchAnalytics } from "./fetchAnalytics";
import { fillStatisticsArray } from "../controllers/awsController";
import ChartJsImage from "chartjs-to-image";

const stripe = new Stripe(
  process.env.NODE_ENV === "production"
    ? process.env.STRIPE_SK_LIVE!
    : process.env.STRIPE_SK_TEST!,
  { apiVersion: "2025-02-24.acacia" },
);

const API_KEY = `${process.env.SENDGRID_APIKEY}`;
const SENDGRID_URL = "https://api.sendgrid.com/v3/marketing/contacts";
const CONFIG = {
  headers: {
    authorization: `Bearer ${API_KEY}`,
    "content-type": "application/json",
  },
};

// AWS API Gateway options
// AWS.config.update({ region: "eu-west-1" });
// const apiGateway = new AWS.APIGateway({
//   accessKeyId: process.env.AWS_ACCESS_KEY,
//   secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
//   endpoint: "https://apigateway.eu-west-1.amazonaws.com",
// });

// TO FIX: Need to query usage of users first and match with those who haven't used any credits.
// export const checkApiUsage = async () => {
// 	let date = new Date();
// 	let firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
// 	let lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
// 	const startDate = formatDate(firstDay.toDateString());
// 	const endDate = formatDate(lastDay.toDateString());

// 	const users24hrs = await pool.query<User>(
// 		`SELECT users.user_id, email, current_usage_plan, api_key, api_key_id from usage_emails INNER JOIN users ON usage_emails.user_id = users.user_id where hrs24_api_not_used is NOT NULL and hrs24_api_not_used < NOW();`
// 	);
// 	users24hrs.rows.forEach(async (user) => {
// 		const params = {
// 			endDate: endDate,
// 			startDate: startDate,
// 			usagePlanId: user.current_usage_plan,
// 			keyId: user.api_key_id,
// 		};

// 		apiGateway.getUsage(params, async (err, data) => {
// 			if (err) console.error("error:", err, err.stack);
// 			else {
// 				const usage: Record<string, AWS.APIGateway.MapOfKeyUsages> = {};
// 				const userId = user.user_id;
// 				if (!data.items) {
// 					return;
// 				}
// 				usage[userId] = { items: data.items };

// 				if (Object.keys(usage[userId]["items"]).length === 0) {
// 					const email = new Email(user);
// 					await email.sendApiNotUsed("24 uur");

// 					await pool.query(
// 						`UPDATE usage_emails SET hrs24_api_not_used=$1 where user_id=$2 RETURNING *`,
// 						[null, user.user_id]
// 					);
// 				} else {
// 					console.log("24hrs usage", usage[userId].items);
// 				}
// 			}
// 		});
// 	});

// 	const users7days = await pool.query(
// 		`SELECT users.user_id, email, current_usage_plan, api_key, api_key_id from usage_emails INNER JOIN users ON usage_emails.user_id = users.user_id where days7_api_not_used is NOT NULL and days7_api_not_used < NOW();`
// 	);
// 	users7days.rows.forEach(async (user) => {
// 		const params = {
// 			endDate: endDate,
// 			startDate: startDate,
// 			usagePlanId: user.current_usage_plan,
// 			keyId: user.api_key_id,
// 		};

// 		await apiGateway.getUsage(params, async (err, data) => {
// 			if (err) console.error("error:", err, err.stack);
// 			else {
// 				const usage = {};
// 				const userId = user.user_id;
// 				usage[userId] = { items: data.items };

// 				if (Object.keys(usage[userId]["items"]).length === 0) {
// 					const email = new Email(user);
// 					await email.sendApiNotUsed("week");

// 					await pool.query(
// 						`UPDATE usage_emails SET days7_api_not_used=$1 where user_id=$2 RETURNING *`,
// 						[null, user.user_id]
// 					);
// 					console.log("7days length 0");
// 				} else {
// 					console.log("7days usage", usage[userId].items);
// 				}
// 			}
// 		});
// 	});
// };

const APIs = [
  "AmenitiesAPI",
  "AVMAPI",
  "EcoValue",
  "LocationAPI",
  "MoveDataAPI",
  "WOZAPI",
  "BagAPI",
  "ReferenceAPI",
  "SustainabilityAPI",
  "InteractiveReference-API",
  "LabellingAPI",
  "TransactionAPI",
  "ObjectDataAPI",
  "EnergyLabelAPI",
  "ObjectGeometryAPI",
];

export const sendEmailIfPrepaidCreditIsDepleted = async () => {
  try {
    const activeUsers = await pool.query<User & UserNotification>(
      `SELECT users.*, user_notifications.credit_depleted
       FROM users 
       INNER JOIN user_notifications ON users.user_id = user_notifications.user_id 
       WHERE users.active = true AND user_notifications.credit_depleted = true AND users.current_usage_plan = $1`,
      [process.env.AWS_MOPSUS_USAGE_PLAN_PPU],
    );

    await Promise.all(activeUsers.rows.map((user) => processUserCredit(user)));
  } catch (error: any) {
    console.error(error);
    throw new AppError(error.message, 400);
  }
};

const processUserCredit = async (user: User & UserNotification) => {
  try {
    const subscriptionId = await getSubscriptionId(user.stripe_customer_id);
    const comingEvent = await incomingInvocice(
      user.stripe_customer_id,
      subscriptionId,
    );
    const subscriptionObject = await getSubscription(subscriptionId);

    if (subscriptionObject.status !== "active") return;

    const invItemCount = comingEvent.lines.data.length - 1;
    const productId = comingEvent.lines.data[invItemCount].plan
      ?.product as string;
    if (!productId) return;

    const product = await getProduct(productId);
    const prepaidCredit = returnPrepaidCredits(product?.name);

    const count = await fetchApiUsage(
      user.api_key_id,
      getMinutesSincePlanChange(user.plan_changed_at),
    );
    if (count <= prepaidCredit || prepaidCredit <= 0) return;

    if (!user.metered_billing) {
      await handleYearlyUsage(user, product.name, prepaidCredit);
    } else {
      await handleUsage(user, product.name, prepaidCredit);
    }

    //await deleteSubscription(subscriptionObject.id);
  } catch (error) {
    console.error(`Failed to process user ${user.email}: ${error}`);
  }
};

const getSubscriptionId = async (customerId: string) => {
  const subscription = await pool.query<UserSubscription>(
    "SELECT subscription_id from user_subscriptions where customer_id=$1",
    [customerId],
  );
  return subscription.rows[0].subscription_id;
};

const getMinutesSincePlanChange = (planChangedAt: Date) => {
  const msSincePlanChange = new Date().getTime() - planChangedAt.getTime();
  return Math.round(msSincePlanChange / 60000);
};

const handleYearlyUsage = async (
  user: User,
  productName: string,
  prepaidCredit: number,
) => {
  const emailYearlySent = await pool.query(
    `SELECT * FROM usage_emails WHERE user_id=$1 and email_80_sent_at < api_extension_updated_at`,
    [user.user_id],
  );
  if (emailYearlySent.rows.length === 0) return;

  new Email(user).sendApiUsageYearlyReached(productName, prepaidCredit, 0);
  await pool.query<{ usage_emails: string }>(
    "UPDATE usage_emails SET email_80_sent_at=to_timestamp($1) where user_id=$2 RETURNING *",
    [Date.now() / 1000, user.user_id],
  );
};

const handleUsage = async (
  user: User,
  productName: string,
  prepaidCredit: number,
) => {
  const emailSent = await pool.query(
    `SELECT * FROM usage_emails WHERE user_id=$1 and email_100_sent_at < api_extension_updated_at`,
    [user.user_id],
  );
  if (emailSent.rows.length === 0) return;

  new Email(user).sendApiUsageReached(productName, prepaidCredit, 0);
  await pool.query<{ usage_emails: string }>(
    "UPDATE usage_emails SET email_100_sent_at=to_timestamp($1) where user_id=$2 RETURNING *",
    [Date.now() / 1000, user.user_id],
  );
};

const returnPrepaidCredits = (stripePlan: string) => {
  switch (stripePlan) {
    case MOPSUS_100:
      return 0;
    case MOPSUS_250:
      return 250;
    case MOPSUS_750:
      return 750;
    case MOPSUS_2000:
      return 2000;
    // Yearly Plan
    case MOPSUS_3000:
      return 3000;
    case MOPSUS_9000:
      return 9000;
    case MOPSUS_24000:
      return 24000;

    // TRANSACTION PLAN
    case TRANSACTION_100:
      return 0;
    default:
      return 0;
  }
};

const returnUsagePlan = (usagePlan: string) => {
  switch (usagePlan) {
    case "812sc7":
      return "Platform - Free Tier";
    case "0bpa4v":
      return "Platform - Pay per use";
  }
};

// Monthly Reset CRON task
export const sendResetApiEmail = async () => {
  try {
    // Query all users in DB
    const users = await pool.query<User>(
      `SELECT * from users WHERE receive_email=$1 AND active=$2;`,
      [true, true],
    );

    if (users.rows.length === 0) {
      throw new AppError("No users found", 400, true);
    }

    const promises = users.rows.map(processUser);
    await Promise.all(promises);
  } catch (error: any) {
    console.error(`Failed to send reset API emails: ${error}`);
    throw new AppError(error.message, 400);
  }
};

const processUser = async (user: User) => {
  try {
    const usagePlan = returnUsagePlan(user.current_usage_plan);

    await pool.query<{ api_extention_update_to: string }>(
      "UPDATE usage_emails set api_extension_updated_at=(to_timestamp($1)),hrs24_api_not_used=(to_timestamp($2)), days7_api_not_used=(to_timestamp($3)) where user_id=$4 RETURNING *",
      [
        Date.now() / 1000,
        (Date.now() + 24 * 3600 * 1000) / 1000,
        (Date.now() + 7 * 24 * 3600 * 1000) / 1000,
        user.user_id,
      ],
    );

    const date = new Date();
    const msSincePlanChange =
      date.getTime() - new Date(user.plan_changed_at).getTime();
    const minutesSincePlanChange = Math.round(msSincePlanChange / 60000);
    const apiKey = user.api_key;

    const count = await fetchApiUsage(apiKey, minutesSincePlanChange);

    if (usagePlan === "Platform - Free Tier") {
      await new Email(user).sendMonthlyResetApiUsageFreeTier();
    }
  } catch (error) {
    console.error(`Failed to process user ${user.email}: ${error}`);
  }
};

export const exhaustedFreeCredits = async () => {
  try {
    const query = await pool.query<User>(
      `SELECT api_key, email, plan_changed_at from users where current_usage_plan=$1;`,
      [`${process.env.AWS_MOPSUS_USAGE_PLAN_FREE}`],
    );

    const promises = query.rows.map(processFreeCredit);
    await Promise.all(promises);
  } catch (error) {
    console.error(`Failed to process users: ${error}`);
  }
};

const processFreeCredit = async (user: User) => {
  try {
    const apiKey = user.api_key;
    const { email } = user;

    const date = new Date();
    const msSincePlanChange =
      date.getTime() - new Date(user.plan_changed_at).getTime();
    const minutesSincePlanChange = Math.round(msSincePlanChange / 60000);
    const minutesSince = minutesSincePlanChange;

    const totalCalls = await fetchApiUsage(apiKey, minutesSince);

    const percent_fifty = (50 * 50) / 100;
    const percent_ninety = (90 * 50) / 100;

    if (totalCalls >= percent_fifty) {
      const body = {
        list_ids: [`${process.env.SENDGRID_LIST_USED50}`],
        contacts: [
          {
            email,
          },
        ],
      };
      await axios.put(SENDGRID_URL, body, CONFIG);
    } else if (totalCalls >= percent_ninety) {
      const body = {
        list_ids: [`${process.env.SENDGRID_LIST_USED90}`],
        contacts: [
          {
            email,
          },
        ],
      };
      await axios.put(SENDGRID_URL, body, CONFIG);
    }
  } catch (error) {
    console.error(
      `Failed to process Free Credit of user ${user.email}: ${error}`,
    );
  }
};

export const checkAPIUsageInactivity = async () => {
  try {
    const userQuery = await pool.query(
      `SELECT email, created_at, api_key, usage_tracked_at, active FROM users`,
    );

    const promises = userQuery.rows.map(processInactivity);
    await Promise.all(promises);
  } catch (error: any) {
    logger.error(error.stack);
  }
};

const processInactivity = async (user: User) => {
  try {
    const currentTime = new Date().getTime();
    const days7 = 7 * 24 * 3600 * 1000;
    const days15 = 15 * 24 * 3600 * 1000;
    const days30 = 30 * 24 * 3600 * 1000;

    let userTime =
      user.usage_tracked_at === null && user.active
        ? new Date(user.created_at).getTime()
        : new Date(user.usage_tracked_at).getTime();

    if (currentTime > userTime + days30) {
      const body = {
        list_ids: [`${process.env.SENDGRID_LIST_UNUSED_DAYS30}`],
        contacts: [
          {
            email: user.email,
          },
        ],
      };
      await axios.put(SENDGRID_URL, body, CONFIG);
    } else if (currentTime > userTime + days15) {
      const body = {
        list_ids: [`${process.env.SENDGRID_LIST_UNUSED_DAYS15}`],
        contacts: [
          {
            email: user.email,
          },
        ],
      };
      await axios.put(SENDGRID_URL, body, CONFIG);
    } else if (currentTime > userTime + days7) {
      const body = {
        list_ids: [`${process.env.SENDGRID_LIST_UNUSED_DAYS7}`],
        contacts: [
          {
            email: user.email,
          },
        ],
      };
      await axios.put(SENDGRID_URL, body, CONFIG);
    }
  } catch (error: any) {
    logger.error(`Failed to process user ${user.email}: ${error}`);
  }
};

export const addPayPerUseUsage = async () => {
  try {
    const usersList = await pool.query(
      `SELECT DISTINCT(customer_id), subscription_item FROM user_subscriptions WHERE active=$1;`,
      [true],
    );

    if (usersList.rows.length === 0) {
      return;
    }

    const updatePromises = usersList.rows.map(async (ppuSubscriber) => {
      const user = await pool.query<User>(
        `SELECT user_id, email, api_key, usage_tracked_at, metered_billing FROM users where stripe_customer_id=$1`,
        [ppuSubscriber.customer_id],
      );

      if (user.rows.length === 0) {
        return;
      }

      const meteredBilling = user.rows[0].metered_billing;

      if (!meteredBilling) {
        await pool.query(
          `UPDATE users SET usage_tracked_at=$1 WHERE stripe_customer_id=$2;`,
          [new Date(Date.now() - 100000), ppuSubscriber.customer_id],
        );
        return;
      }

      const date = new Date();
      const msSincePlanChange =
        date.getTime() - new Date(user.rows[0].usage_tracked_at).getTime();
      const minutesSincePlanChange = Math.round(msSincePlanChange / 60000);

      const apikey = user.rows[0].api_key;

      const count = await fetchApiUsage(apikey, minutesSincePlanChange);
      const result = await createUsage(ppuSubscriber.subscription_item, count);

      if (result) {
        await pool.query(
          `UPDATE users SET usage_tracked_at=$1 WHERE stripe_customer_id=$2;`,
          [new Date(Date.now() - 100000), ppuSubscriber.customer_id],
        );
      }
    });

    await Promise.all(updatePromises);
  } catch (error: any) {
    logger.error(error.stack);
    return new AppError(error.message, 500);
  }
};

export const addPayPerUseUsageTransaction = async () => {
  try {
    const usersList = await pool.query(
      `SELECT DISTINCT(customer_id), subscription_item FROM transaction_subscriptions WHERE active=$1;`,
      [true],
    );

    if (usersList.rows.length === 0) {
      return;
    }

    const updatePromises = usersList.rows.map(async (ppuSubscriber) => {
      const user = await pool.query(
        `SELECT * FROM users where stripe_customer_id=$1`,
        [ppuSubscriber.customer_id],
      );

      if (user.rows.length === 0) {
        return;
      }

      const transactionApiUser = await pool.query(
        `select * from transaction_subscriptions where customer_id=$1`,
        [ppuSubscriber.customer_id],
      );

      if (transactionApiUser.rows.length === 0) {
        return;
      }

      const date = new Date();
      const msSincePlanChange =
        date.getTime() - transactionApiUser.rows[0].usage_tracked_at;
      const minutesSincePlanChange = Math.round(msSincePlanChange / 60000);

      const apikey = user.rows[0].transaction_api_key;

      return fetchApiUsage(apikey, minutesSincePlanChange)
        .then((count) => createUsage(ppuSubscriber.subscription_item, count))
        .then(async (result) => {
          if (result) {
            await pool.query(
              `UPDATE transaction_subscriptions SET usage_tracked_at=$1 WHERE customer_id=$2;`,
              [new Date(Date.now() - 100000), ppuSubscriber.customer_id],
            );
          }
        });
    });

    await Promise.all(updatePromises);
  } catch (error: any) {
    logger.error(error.stack);
    return new AppError(error.message, 500);
  }
};

export const updatePlanChangedAt = async () => {
  try {
    const usersList = await pool.query(
      `SELECT plan_changed_at FROM users WHERE active=$1 AND current_usage_plan=$2;`,
      [true, "812sc7"],
    );

    if (usersList.rowCount === 0) {
      return;
    }

    const updatePromises = usersList.rows.map((user) => {
      return pool.query<User>(
        `UPDATE users SET plan_changed_at=to_timestamp($1) WHERE user_id=$2 RETURNING *;`,
        [new Date(), user.user_id],
      );
    });

    await Promise.all(updatePromises);
  } catch (error: any) {
    if (error instanceof Error) {
      logger.error(error.stack);
      return new AppError(error.message, 500);
    }

    throw error;
  }
};

export const sendEmailByStatusCode = async (
  statusCode: string,
  templateId: string,
  sendStatusField: string,
) => {
  try {
    const data = await fetchApiKeyByStatusCode(statusCode);
    if (data.length === 0) {
      return;
    }

    const emailPromises = data.map(async (item: { apiKey: string }) => {
      const { apiKey } = item;
      const keyLength = apiKey.length;
      const last6 = apiKey.slice(keyLength - 6);

      const user = await pool.query<User & UserNotification>(
        `SELECT users.*, user_notifications.${sendStatusField}
         FROM users 
         INNER JOIN user_notifications ON users.user_id = user_notifications.user_id 
         WHERE users.api_key LIKE $1 AND user_notifications.${sendStatusField} = true`,
        [`%${last6}`],
      );

      if (user.rowCount === 0) {
        console.log("User not found or email preference is false");
        return;
      }

      await new Email(user.rows[0]).send4xxEmail(templateId);
    });

    await Promise.all(emailPromises);
  } catch (error: any) {
    console.error(error);
  }
};

export const send429Email = () =>
  sendEmailByStatusCode("429", SENDGRID_429_ID, "error_429");
export const send403Email = () =>
  sendEmailByStatusCode("403", SENDGRID_403_ID, "error_403");
export const send500Email = () =>
  sendEmailByStatusCode("500", SENDGRID_500_ID, "error_500");
export const send422Email = () =>
  sendEmailByStatusCode("422", SENDGRID_422_ID, "error_422");

export const updateCustomerNameOnStripe = async () => {
  try {
    const users = await pool.query<User>(
      `SELECT * FROM users WHERE active=$1`,
      [true],
    );
    await Promise.all(
      users.rows.map((user) =>
        stripe.customers.update(user.stripe_customer_id, {
          name: user.company,
        }),
      ),
    );
  } catch (error) {
    console.log(error);
  }
};

export const getFormattedDate = (daysFromNow: number): string => {
  const date = new Date();
  date.setDate(date.getDate() + daysFromNow);
  return date.toISOString().slice(0, 10);
};

const AWS_MOPSUS_USAGE_TRANSACTION_100 =
  process.env.AWS_MOPSUS_USAGE_TRANSACTION_100;

export const sendAnalytics = async (days: number) => {
  try {
    const endDate = getFormattedDate(1);
    const startDate = getFormattedDate(-days);

    const users = await pool.query<User & UserNotification>(
      `SELECT users.*, user_notifications.analytics_email_usage
       FROM users 
       INNER JOIN user_notifications ON users.user_id = user_notifications.user_id 
       WHERE (users.current_usage_plan != '812sc7' OR users.transaction_usage_plan = $1)
       AND users.receive_email = true
       AND user_notifications.analytics_email_usage = true`,
      [AWS_MOPSUS_USAGE_TRANSACTION_100],
    );

    await Promise.all(
      users.rows.map((user) =>
        processUserAnalytics(user, startDate, endDate, days),
      ),
    );
  } catch (error) {
    console.error(`Failed to send analytics: ${error}`);
  }
};

const processUserAnalytics = async (
  user: User & UserNotification,
  startDate: string,
  endDate: string,
  days: number,
) => {
  try {
    const { api_key: apikey1, transaction_api_key: apikey2 } = user;
    const analytics = await fetchAnalytics(
      apikey1,
      apikey2,
      startDate,
      endDate,
    );
    const data = fillStatisticsArray(analytics, days);
    const labels = data.map((obj) => obj.day);

    const datasets = APIs.map((api) => ({
      label: api,
      data: data.map((obj) => obj[api]),
      stack: "Stack 0",
    }));

    const chart = new ChartJsImage();
    chart.setConfig({
      type: "bar",
      data: {
        labels,
        datasets,
      },
      options: {
        responsive: true,
        scales: {
          x: {
            beginAtZero: true,
          },
          y: {
            beginAtZero: true,
            stacked: true,
          },
        },
      },
    });
    const url = await chart.toDataUrl();
    const image = `<img src=${url} alt='analytics' />`;
    await new Email(user).sendAnalytics(image);
  } catch (userError) {
    console.error(`Failed to process user ${user.email}: ${userError}`);
  }
};
