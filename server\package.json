{"name": "mopsus", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "test": "jest --watchAll --verbose", "client": "cd ../ && cd client && npm start", "server": "cross-env NODE_ENV=development nodemon server.ts", "client:build": "cd client && npm install && npm run build", "start:dev": "concurrently \"npm run server\" \"npm run client\"", "start:prod": "export NODE_ENV=production npm run client:build && npm install && npm run start", "start:test": "export NODE_ENV=test npm run client:build && npm install && npm run start", "migrate": "ts-node scripts/migrate.ts", "migrate:prod": "NODE_ENV=production ts-node scripts/migrate.ts"}, "author": "<PERSON> and <PERSON><PERSON>", "license": "ISC", "dependencies": {"@amplitude/node": "^1.8.4", "@aws-sdk/client-api-gateway": "^3.40.0", "@aws-sdk/client-cloudwatch": "^3.40.0", "@elastic/elasticsearch": "^8.14.0", "@google/generative-ai": "^0.17.1", "@mui/icons-material": "^5.11.16", "@sendgrid/mail": "^7.6.2", "@types/stream-buffers": "^3.0.7", "archiver": "^6.0.1", "aws-sdk": "^2.1414.0", "axios": "^0.26.1", "bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "chartjs-to-image": "^1.2.1", "concurrently": "^7.0.0", "connect-pg-simple": "^7.0.0", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^8.6.0", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "express-session": "^1.17.3", "express-validator": "^6.6.1", "fs-extra": "^11.2.0", "graphql": "^15.7.1", "graphql-request": "^3.6.1", "helmet": "^4.1.0", "html-to-text": "^8.1.0", "jsonwebtoken": "^9.0.0", "multer": "^1.4.5-lts.1", "nanoid": "^3.1.23", "node-cron": "^2.0.3", "node-pg-migrate": "^5.9.0", "nodemailer": "^6.4.11", "nodemon": "^2.0.19", "openai": "^4.19.0", "passport": "^0.6.0", "passport-google-oauth2": "^0.2.0", "passport-google-oauth20": "^2.0.0", "passport-linkedin-oauth2": "^2.0.0", "pg": "^8.11.3", "pug": "^3.0.0", "qrcode": "^1.5.4", "speakeasy": "^2.0.0", "stream-buffers": "^3.0.3", "stripe": "^17.7.0", "styled-components": "^5.3.5", "tippy.js": "^6.3.7", "together-ai": "^0.6.0-alpha.4", "uuid": "^10.0.0", "uuidv4": "^6.2.13", "winston": "^3.3.3", "xss-clean": "^0.1.1"}, "devDependencies": {"@types/archiver": "^6.0.2", "@types/bcryptjs": "^2.4.2", "@types/busboy": "^1.5.4", "@types/cookie-parser": "^1.4.3", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/express-session": "^1.17.7", "@types/fs-extra": "^11.0.4", "@types/html-to-text": "^9.0.0", "@types/jest": "^29.5.0", "@types/jsonwebtoken": "^9.0.1", "@types/multer": "^1.4.12", "@types/node-cron": "^3.0.7", "@types/nodemailer": "^6.4.7", "@types/passport": "^1.0.12", "@types/passport-google-oauth20": "^2.0.11", "@types/passport-linkedin-oauth2": "^1.5.3", "@types/pug": "^2.0.6", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "chai": "^5.1.1", "eslint": "^8.2.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "jest": "^27.5.1", "mocha": "^10.7.3", "supertest": "^6.2.2", "ts-node": "^10.9.2", "typescript": "^5.5.4"}}