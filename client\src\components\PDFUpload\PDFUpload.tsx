import React, { FC, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { GoPlus } from "react-icons/go";
import { FaFilePdf } from "react-icons/fa";

interface PDFUploadProps {
  onFileSelected: (file: File) => void;
  selectedFile?: File;
}

const PDFUpload: FC<PDFUploadProps> = ({ onFileSelected, selectedFile }) => {
  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        onFileSelected(acceptedFiles[0]);
      }
    },
    [onFileSelected]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    multiple: false,
    maxSize: 10 * 1024 * 1024, // 10MB max file size
  });

  return (
    <div className="pdf-upload-container">
      {selectedFile && (
        <div className="selected-file-preview">
          <div className="file-info">
            <FaFilePdf size={48} color="#e74c3c" />
            <div className="file-details">
              <p className="file-name">{selectedFile.name}</p>
              <p className="file-size">
                {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
              </p>
            </div>
          </div>
        </div>
      )}
      
      <div
        {...getRootProps()}
        className={`dropzone ${isDragActive ? 'active' : ''}`}
        style={{
          border: "2px dashed #1e9d66",
          width: "100%",
          height: "230px",
          minWidth: "280px",
          maxWidth: "600px",
          padding: "20px",
          borderRadius: "8px",
          textAlign: "center",
          alignItems: "center",
          display: "flex",
          flexDirection: "column",
          gap: "1rem",
          justifyContent: "center",
          cursor: "pointer",
          backgroundColor: isDragActive ? "#f0f9f4" : "transparent",
        }}
      >
        <input {...getInputProps()} />
        <FaFilePdf size={48} color="#9a9a9a" />
        <div style={{ color: "#9a9a9a", textAlign: "center" }}>
          <p>
            {isDragActive
              ? "Drop het PDF bestand hier..."
              : "Sleep een PDF bestand hierheen of klik om te selecteren"}
          </p>
          <p style={{ fontSize: "0.9em", marginTop: "0.5rem" }}>
            Alleen PDF bestanden (max 10MB)
          </p>
        </div>
        <GoPlus size={32} color="#9a9a9a" />
      </div>
    </div>
  );
};

export default PDFUpload;
