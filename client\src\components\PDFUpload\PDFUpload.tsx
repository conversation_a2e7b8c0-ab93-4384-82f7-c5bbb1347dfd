import React, { FC, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { GoPlus } from "react-icons/go";
import { FaFilePdf } from "react-icons/fa";

interface PDFUploadProps {
  onFileSelected: (file: File) => void;
  selectedFile?: File;
}

const PDFUpload: FC<PDFUploadProps> = ({ onFileSelected, selectedFile }) => {
  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        onFileSelected(acceptedFiles[0]);
      }
    },
    [onFileSelected],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: "application/pdf",
    multiple: false,
    maxSize: 10 * 1024 * 1024, // 10MB max file size
  });

  return (
    <div className="my-8">
      {selectedFile && (
        <div className="mb-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <div className="flex items-center gap-4">
            <FaFilePdf size={48} color="#e74c3c" />
            <div className="flex-1">
              <p className="font-semibold text-gray-800 mb-1">
                {selectedFile.name}
              </p>
              <p className="text-gray-600 text-sm">
                {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
              </p>
            </div>
          </div>
        </div>
      )}

      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed border-primary w-full h-58 min-w-72 max-w-2xl
          p-5 rounded-lg text-center flex flex-col items-center gap-4 justify-center
          cursor-pointer transition-all duration-300 ease-in-out
          ${isDragActive ? "border-primary bg-green-50" : "bg-transparent"}
        `}
      >
        <input {...getInputProps()} />
        <FaFilePdf size={48} className="text-gray-400" />
        <div className="text-gray-400 text-center">
          <p className="mb-2">
            {isDragActive
              ? "Drop het PDF bestand hier..."
              : "Sleep een PDF bestand hierheen of klik om te selecteren"}
          </p>
          <p className="text-sm">Alleen PDF bestanden (max 10MB)</p>
        </div>
        <GoPlus size={32} className="text-gray-400" />
      </div>
    </div>
  );
};

export default PDFUpload;
