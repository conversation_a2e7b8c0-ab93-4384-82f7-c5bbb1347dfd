import AWS from "aws-sdk";
import fs from "fs";
import axios, { AxiosResponse } from "axios";
import {
  APIGatewayClient,
  DeleteUsagePlanKeyCommand,
  CreateUsagePlanKeyCommand,
  CreateApiKeyCommand,
  GetRestApiCommand,
} from "@aws-sdk/client-api-gateway";
import { nanoid } from "nanoid";
import { NextFunction, Request, Response } from "express";
import AppError from "../utils/appError";
import pool from "../db";
import fetchApiUsage from "../utils/fetchApiUsage";
import { fetchAnalytics } from "../utils/fetchAnalytics";
import { withExponentialBackoff } from "../utils/awsRetry";
import convertTimeStamp from "../utils/convertTimeStamp";
import logger from "../utils/logger";
import {
  AuthRequest,
  Statistics,
  UnlimitedSubscription,
  User,
  UserSubscription,
} from "../@types";
import {
  AWS_MOPSUS_USAGE_AVM_UNLIMITED,
  AWS_MOPSUS_USAGE_ECO_UNLIMITED,
  AWS_MOPSUS_USAGE_ENERGY_UNLIMITED,
  AWS_MOPSUS_USAGE_OBJ_DATA_UNLIMITED,
  AWS_MOPSUS_USAGE_REFERENCE_UNLIMITED,
  AWS_MOPSUS_USAGE_WOZ_UNLIMITED,
} from "../utils/constants";
import { fetchUsageByTime } from "../utils/fetchUsageTimeRange";
import getTimeBoundaries from "../utils/timeBoundries";
import { SubscriptionTable } from "./stripeController";
import fetchErrorCount from "../utils/fetchErrorCount";
import { URL } from "url";
import fetchApiLog from "../utils/fetchApiLog";
import { convertDate } from "../utils/convertDate";

const client = new APIGatewayClient({
  region: "eu-west-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

// AWS API Gateway options
AWS.config.update({ region: "eu-west-1" });
const apiGateway = new AWS.APIGateway({
  accessKeyId: process.env.AWS_ACCESS_KEY,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  endpoint: "https://apigateway.eu-west-1.amazonaws.com",
});

const cloudWatch = new AWS.CloudWatch({
  secretAccessKey: process.env.AWS_CLOUDWATCH_SECRET_ACCESS_KEY,
  accessKeyId: process.env.AWS_CLOUDWATCH_ACCESS_KEY_ID,
});

const setMetricParams = (startDate: string, apiValue: string) => ({
  StartTime: new Date(startDate),
  EndTime: new Date(),
  Period: 3600,
  Namespace: "AWS/ApiGateway",
  MetricName: "Count",
  Dimensions: [{ Name: "ApiName", Value: apiValue }],
  Statistics: ["SampleCount"],
});

const getApiStatistics = async (startDate: string, apiValue: string) => {
  const apiData = new Promise(async (resolve, reject) => {
    cloudWatch.getMetricStatistics(
      setMetricParams(startDate, apiValue),
      async (err, data) => {
        if (err) reject(err);
        else {
          const apiTotal = data.Datapoints?.reduce(
            (apiTotal, item) => apiTotal + item.SampleCount!,
            0,
          );
          resolve(apiTotal);
        }
      },
    );
  });
  return await apiData;
};

export const getUsagePlanDetails = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user as User;
    const usagePlanId = user.current_usage_plan;
    const transactionApiKey = user.transaction_api_key;
    const transactionUsageId = user.transaction_usage_plan;
    const planChangedAt = new Date(user.plan_changed_at);
    const now = new Date();

    const msSincePlanChange = now.getTime() - planChangedAt.getTime();
    const minutesSincePlanChange = Math.round(msSincePlanChange / 60000);

    const {
      lastWeekBeginning,
      thisWeekBeginning,
      yesterdayBeginning,
      todayBeginning,
      rightNow,
    } = getTimeBoundaries();

    const [
      usagePlan,
      usageData,
      usageYesterday,
      usageToday,
      usageLastWeek,
      usageThisWeek,
    ] = await Promise.all([
      withExponentialBackoff<AWS.APIGateway.UsagePlan>(
        () =>
          new Promise((resolve, reject) => {
            apiGateway.getUsagePlan({ usagePlanId }, (err, data) => {
              if (err) reject(err);
              else resolve(data);
            });
          }),
      ),
      fetchApiUsage(user.api_key, minutesSincePlanChange),
      fetchUsageByTime(user.api_key, yesterdayBeginning, todayBeginning),
      fetchUsageByTime(user.api_key, todayBeginning, rightNow),
      fetchUsageByTime(user.api_key, lastWeekBeginning, thisWeekBeginning),
      fetchUsageByTime(user.api_key, thisWeekBeginning, rightNow),
    ]);

    const apiDetails = await Promise.all(
      usagePlan.apiStages?.map(async (stage) => {
        try {
          const apiInfo = await withExponentialBackoff(() =>
            client.send(
              new GetRestApiCommand({
                restApiId: stage.apiId,
              }),
            ),
          );
          return {
            id: stage.apiId,
            name: apiInfo.name,
            stage: stage.stage,
          };
        } catch (error) {
          console.error(`Failed to get API ${stage.apiId}:`, error);
          return {
            id: stage.apiId,
            name: "Unknown API",
            stage: stage.stage,
          };
        }
      }) || [],
    );

    const allowedApiNames = apiDetails.map((d) => d.name);

    let transactionData;
    let transactionUsageYesterday;
    let transactionUsageToday;
    let transactionUsageLastWeek;
    let transactionUsageThisWeek;

    if (transactionUsageId !== null) {
      const transactionQuery = await pool.query(
        `SELECT * FROM transaction_subscriptions WHERE customer_id=$1;`,
        [user.stripe_customer_id],
      );
      if (transactionQuery.rowCount !== 0) {
        const transactionPlanChangedAt = new Date(
          transactionQuery.rows[0].plan_changed_at,
        );
        const msSinceTransactionPlanChange =
          now.getTime() - transactionPlanChangedAt.getTime();
        const minutesSinceTransactionPlanChange = Math.round(
          msSinceTransactionPlanChange / 60000,
        );

        const [
          transactionDataResult,
          transactionUsageTodayResult,
          transactionUsageYesterdayResult,
          transactionUsageThisWeekResult,
          transactionUsageLastWeekResult,
        ] = await Promise.all([
          transactionApiKey &&
            fetchApiUsage(transactionApiKey, minutesSinceTransactionPlanChange),
          transactionApiKey &&
            fetchUsageByTime(transactionApiKey, todayBeginning, rightNow),
          transactionApiKey &&
            fetchUsageByTime(
              transactionApiKey,
              yesterdayBeginning,
              todayBeginning,
            ),
          transactionApiKey &&
            fetchUsageByTime(transactionApiKey, thisWeekBeginning, rightNow),
          transactionApiKey &&
            fetchUsageByTime(
              transactionApiKey,
              lastWeekBeginning,
              thisWeekBeginning,
            ),
        ]);

        transactionData = transactionDataResult;
        transactionUsageToday = transactionUsageTodayResult;
        transactionUsageYesterday = transactionUsageYesterdayResult;
        transactionUsageThisWeek = transactionUsageThisWeekResult;
        transactionUsageLastWeek = transactionUsageLastWeekResult;
      }
    }

    res.status(200).json({
      status: "success",
      subscription: usagePlanId !== "812sc7",
      usage: usageData,
      usageToday,
      usageYesterday,
      usageLastWeek,
      usageThisWeek,
      transactionUsage: transactionData,
      transactionUsageToday,
      transactionUsageYesterday,
      transactionUsageThisWeek,
      transactionUsageLastWeek,
      transactionSubscription:
        transactionUsageId === process.env.AWS_MOPSUS_USAGE_TRANSACTION_PPU,
      usagePlan: {
        name: usagePlan.name,
        quota: usagePlan.quota != null ? usagePlan.quota.limit : null,
      },
      allowedApiNames:
        transactionUsageId !== null
          ? [...allowedApiNames, "TransactionAPI", "ECP"]
          : allowedApiNames,
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
    console.log(error);
  }
};

export const updateUsagePlanKeyOnSubscription = async (
  apiKeyId: string,
  removeUsagePlanId: string,
  addUsagePlanId: string,
  email: string,
) => {
  try {
    await changeUsagePlan(apiKeyId, removeUsagePlanId, addUsagePlanId);

    logger.info("updating user current plan id on the database");

    const updatedUser = await pool.query<User>(
      "UPDATE users SET current_usage_plan=$1 where email=$2 RETURNING *",
      [addUsagePlanId, email],
    );

    await pool.query<User>(
      "UPDATE usage_emails set api_extension_updated_at=(to_timestamp($1)),hrs24_api_not_used=(to_timestamp($2)), days7_api_not_used=(to_timestamp($3)) where user_id=$4 RETURNING *",
      [
        Date.now() / 1000,
        (Date.now() + 24 * 3600 * 1000) / 1000,
        (Date.now() + 7 * 24 * 3600 * 1000) / 1000,
        updatedUser.rows[0].user_id,
      ],
    );
    return { status: "success", received: true };
  } catch (error: any) {
    logger.error(error.stack);
    return { status: "failed", received: false };
  }
};

async function createApiKey(
  email: string,
  keyPrefix: string,
  keyField: string,
  idField: string,
  customer_id: string,
  customerIdColumn: string,
  table: string,
) {
  const apiKeyName = `Mopsus-${email}-${keyPrefix}-${nanoid(10)}`;
  const command = new CreateApiKeyCommand({ name: apiKeyName, enabled: true });
  const response = await withExponentialBackoff(() => client.send(command));
  if (!response.id) {
    throw new Error(`Failed to create API key for email: ${email}`);
  }
  if (table === "users") {
    await pool.query<User>(
      `UPDATE ${table} SET ${keyField}=$1, ${idField}=$2 WHERE ${customerIdColumn}=$3`,
      [response.value, response.id, customer_id],
    );
  } else {
    await pool.query<UserSubscription>(
      `INSERT INTO ${table} (subscription_id, subscription_item, ${keyField}, ${idField}, ${customerIdColumn}) VALUES($1, $2, $3, $4, $5) RETURNING*`,
      [
        "this_is_a_random_id",
        "subscription_item",
        response.value,
        response.id,
        customer_id,
      ],
    );
  }
  return response.id;
}

async function manageUsagePlan(
  stripe_customer_id: string,
  customerIdColumn: string,
  apiKeyId: string,
  oldPlanId: string | null,
  newPlanId: string | null,
  planField: string,
  table: string,
) {
  if (oldPlanId) {
    const deleteCommand = new DeleteUsagePlanKeyCommand({
      keyId: apiKeyId,
      usagePlanId: oldPlanId,
    });
    await withExponentialBackoff(() => client.send(deleteCommand));
    await pool.query(
      `UPDATE ${table} SET ${planField}= NULL WHERE ${customerIdColumn}=$1`,
      [stripe_customer_id],
    );
    logger.info(`${planField} has been deleted`);
  }
  if (newPlanId) {
    const createCommand = new CreateUsagePlanKeyCommand({
      keyId: apiKeyId,
      keyType: "API_KEY",
      usagePlanId: newPlanId,
    });
    await withExponentialBackoff(() => client.send(createCommand));
    await pool.query(
      `UPDATE ${table} SET ${planField}=$1 WHERE ${customerIdColumn}=$2`,
      [newPlanId, stripe_customer_id],
    );
    logger.info(`${planField} has been created`);
  }
  return { status: "success", received: true };
}

export const updateUsagePlanKeyOnUnlimitedSubscription = async (
  user: User,
  table: string,
  changedPlan: string,
) => {
  try {
    logger.info("Unlimited subscription is working");
    const { email, stripe_customer_id } = user;
    const subscription = await pool.query<UnlimitedSubscription>(
      `SELECT * FROM ${table} WHERE customer_id=$1`,
      [stripe_customer_id],
    );
    let apiKeyId: string;
    let usagePlan: string | null;
    let comingPlan: string | null;
    if (subscription.rowCount === 0) {
      apiKeyId = await createApiKey(
        email,
        "unlimited",
        "api_key",
        "api_key_id",
        stripe_customer_id,
        "customer_id",
        table,
      );
      usagePlan = null;
      comingPlan = changedPlan;
    } else {
      const { api_key_id, usage_plan } = subscription.rows[0];
      apiKeyId = api_key_id;
      usagePlan = usage_plan;
      comingPlan = null;
    }

    return await manageUsagePlan(
      stripe_customer_id,
      "customer_id",
      apiKeyId,
      usagePlan,
      comingPlan,
      "usage_plan",
      table,
    );
  } catch (error: any) {
    logger.error(
      `Error while updating unlimited subscription: ${error.message}`,
    );
    return { status: "failed", received: false };
  }
};

export const updateUsagePlanKeyOnTransactionSubscription = async (
  user: User,
  changedPlan: string,
) => {
  try {
    logger.info("Transaction subscription is working");
    const {
      email,
      transaction_api_key_id,
      transaction_usage_plan,
      stripe_customer_id,
    } = user;

    let apiKeyId = transaction_api_key_id;
    if (!apiKeyId) {
      apiKeyId = await createApiKey(
        email,
        "transaction",
        "transaction_api_key",
        "transaction_api_key_id",
        stripe_customer_id,
        "stripe_customer_id",
        "users",
      );
    }
    return await manageUsagePlan(
      stripe_customer_id,
      "stripe_customer_id",
      apiKeyId,
      transaction_usage_plan,
      changedPlan,
      "transaction_usage_plan",
      "users",
    );
  } catch (error: any) {
    logger.error(
      `Error while updating transaction subscription: ${error.message}`,
    );
    return { status: "failed", received: false };
  }
};

export const getTotalApiCalls = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const startDate = new Date().toISOString().slice(0, 10);
    const apiMap = new Map();
    const apiList = [
      "avm",
      "eco-value-v2",
      "woz",
      "autosuggest",
      "location",
      "move-data",
      "amenities",
      "bag",
      "epc",
    ];

    const apiPromises = apiList.map(
      async (api) => await getApiStatistics(startDate, api),
    );
    const apiPromiseCompleted = await Promise.all(apiPromises);
    apiPromiseCompleted.forEach((api, idx) => {
      apiMap.set(apiList[idx], api);
    });

    let sumApiCalls = 0;
    apiMap.forEach((api) => {
      sumApiCalls += api;
    });

    res.status(200).json({
      status: "success",
      sumApiCalls,
    });
  } catch (error: any) {
    logger.error(error.stack);
    next(new AppError(error.message, 500, false));
  }
};

export const apiLogs = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user as User;
    const planChangedAt = new Date(user.plan_changed_at);
    const now = new Date();
    const msSincePlanChange = now.getTime() - planChangedAt.getTime();
    const minutesSincePlanChange = Math.round(msSincePlanChange / 60000);
    const logs = await fetchApiLog(user.api_key, minutesSincePlanChange);
    const logsWithoutTimestamps = logs.map(({ timestamp, ...rest }) => rest);
    res.json(logsWithoutTimestamps);
  } catch (error: any) {
    console.log(error);
    next(new AppError(error.message, 400));
  }
};

export const getCallsAnalytics = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const statistics: Record<any, any> = [];
    const days = parseInt(req.params.days);
    const endDate = new Date(new Date().setDate(new Date().getDate() + 1))
      .toISOString()
      .slice(0, 10);
    const startDate = new Date(new Date().setDate(new Date().getDate() - days))
      .toISOString()
      .slice(0, 10);

    if (!req.headers["x-api-key"]) return;
    const apiKey = req.headers["x-api-key"].toString();
    if (!req.headers["x-api-key-2"]) return;
    const apiKey2 = req.headers["x-api-key-2"].toString();

    const response = await fetchAnalytics(apiKey, apiKey2, startDate, endDate);
    const success = await fetchErrorCount("200", days, apiKey);
    const res400 = await fetchErrorCount("400", days, apiKey);
    const res500 = await fetchErrorCount("500", days, apiKey);
    const res422 = await fetchErrorCount("422", days, apiKey);
    const res429 = await fetchErrorCount("429", days, apiKey);

    const responseStatus = {
      "400: Geen geslaagde aanvraag": res400,
      "429: Te veel verzoeken": res429,
      "422: Input validatie error": res422,
      "500: Probleem met API": res500,
      "200: Succesvolle aanvraag": success,
    };

    let i = days;
    while (i >= 0) {
      let date = new Date(new Date().setDate(new Date().getDate() - i))
        .toISOString()
        .slice(0, 10);
      const [day, time] = date.split(" ");

      statistics.push({
        day: date,
        calls: 0,
        apiStats: {
          AmenitiesAPI: 0,
          AVMAPI: 0,
          LocationAPI: 0,
          MoveDataAPI: 0,
          WOZAPI: 0,
          BagAPI: 0,
          ReferenceAPI: 0,
          SustainabilityAPI: 0,
          "InteractiveReference-API": 0,
          LabellingAPI: 0,
          TransactionAPI: 0,
          ObjectDataAPI: 0,
          EnergyLabelAPI: 0,
          ObjectGeometryAPI: 0,
          "AVM+": 0,
          "OBJECT-GEOMETRY-API": 0,
          AutoSuggestAPI: 0,
          EnergyClimateAPI: 0,
          EnergyDataAPI: 0,
          EnergyInsightAPI: 0,
          RebuildAPI: 0,
          EPCAPI: 0,
        },
      });

      response.forEach(
        (item: { beginTimeSeconds: number; api_name: any; count: number }) => {
          if (convertTimeStamp(item.beginTimeSeconds) == date) {
            statistics[days - i].apiStats[item.api_name] = item.count;
          }
        },
      );

      for (const [key, value] of Object.entries(
        statistics[days - i].apiStats,
      )) {
        if (key !== "day" && key !== "calls") {
          statistics[days - i].calls += value;
        }
      }

      date = new Date(new Date().setDate(new Date().getDate() - i))
        .toLocaleString("nl")
        .split(", ")[0];
      statistics[days - i].day = convertDate(date);
      i--;
    }
    return res.status(200).json({ statistics, responseStatus });
  } catch (error: any) {
    console.log(error);
    next(new AppError(error.message, 500, false));
  }
};

export const fillStatisticsArray = (response: any, days: number) => {
  interface Statistic {
    day: string;
    calls: number;
    AmenitiesAPI: number;
    AVMAPI: number;
    EcoValue: number;
    LocationAPI: number;
    MoveDataAPI: number;
    WOZAPI: number;
    BagAPI: number;
    ReferenceAPI: number;
    SustainabilityAPI: number;
    "InteractiveReference-API": number;
    LabellingAPI: number;
    TransactionAPI: number;
    ObjectDataAPI: number;
    EnergyLabelAPI: number;
    ObjectGeometryAPI: number;
    EPCAPI: number;
    [key: string]: string | number;
  }

  const statistics: Statistic[] = [];
  let i = days;

  while (i >= 0) {
    const date = new Date(new Date().setDate(new Date().getDate() - i))
      .toISOString()
      .slice(0, 10);
    statistics.push({
      day: date,
      calls: 0,
      AmenitiesAPI: 0,
      AVMAPI: 0,
      EcoValue: 0,
      LocationAPI: 0,
      MoveDataAPI: 0,
      WOZAPI: 0,
      BagAPI: 0,
      ReferenceAPI: 0,
      SustainabilityAPI: 0,
      "InteractiveReference-API": 0,
      LabellingAPI: 0,
      TransactionAPI: 0,
      ObjectDataAPI: 0,
      EnergyLabelAPI: 0,
      ObjectGeometryAPI: 0,
      EPCAPI: 0,
    });

    response.forEach(
      (item: { beginTimeSeconds: number; api_name: string; count: number }) => {
        if (convertTimeStamp(item.beginTimeSeconds) == date) {
          statistics[days - i][item.api_name] = item.count;
        }
      },
    );

    for (const [key, value] of Object.entries(statistics[days - i])) {
      if (key !== "day") {
        statistics[days - i].calls += value as number;
      }
    }

    statistics[days - i].day = new Date(
      new Date().setDate(new Date().getDate() - i),
    )
      .toLocaleString("nl-NL")
      .split(" ")[0];

    i--;
  }

  return statistics;
};

export async function changeUsagePlan(
  apiKeyId: string,
  removeUsagePlanId: string,
  addUsagePlanId: string,
) {
  try {
    logger.info("Deleting usage plan");
    await withExponentialBackoff(() =>
      client.send(
        new DeleteUsagePlanKeyCommand({
          keyId: apiKeyId,
          usagePlanId: removeUsagePlanId,
        }),
      ),
    );
    logger.info("Usage plan deleted");

    logger.info("Creating usage plan");
    await withExponentialBackoff(() =>
      client.send(
        new CreateUsagePlanKeyCommand({
          keyId: apiKeyId,
          keyType: "API_KEY",
          usagePlanId: addUsagePlanId,
        }),
      ),
    );
    logger.info("Usage plan created");
  } catch (err) {
    console.log("ChangeUsagePlan", err);
  }
}
