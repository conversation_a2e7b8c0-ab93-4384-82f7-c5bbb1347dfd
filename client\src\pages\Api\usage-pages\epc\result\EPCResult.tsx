import React from "react";
import { useHistory } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../../../../redux/hooks";
import {
  clearEPCResults,
  modifyEPCQueries,
} from "../../../../../redux/actions/epcActions";
import { EPCResult as EPCResultType } from "../types";
import { Redirect } from "react-router-dom";
import RedoButton from "../../components/RedoButtons";

const EPCResult = () => {
  const { result, loading } = useAppSelector((state) => state.epc);
  const history = useHistory();
  const dispatch = useAppDispatch();

  const clearResults = () => {
    dispatch(clearEPCResults());
    history.push("/epc");
  };

  const modifyResults = () => {
    dispatch(modifyEPCQueries());
    history.push("/epc");
  };

  if (Object.keys(result).length === 0 && !loading) {
    return <Redirect to="/epc" />;
  }

  const property = result as EPCResultType;

  const formatValue = (value: any): string => {
    if (value === null || value === undefined) return "-";
    if (typeof value === "number") return value.toString();
    return value;
  };

  const basicInfo = [
    { label: "Postcode", value: formatValue(property.post_code) },
    { label: "Huisnummer", value: formatValue(property.house_number) },
    {
      label: "Huisnummer toevoeging",
      value: formatValue(property.house_addition),
    },
    { label: "Bouwjaar", value: formatValue(property.build_year) },
    { label: "Woningtype", value: formatValue(property.house_type) },
    {
      label: "Woonoppervlakte (m²)",
      value: formatValue(property.inner_surface_area),
    },
  ];

  const energyInfo = [
    {
      label: "Huidig energielabel",
      value: formatValue(property.current_estimated_energy_label),
    },
    {
      label: "Definitief energielabel",
      value: formatValue(property.definitive_energy_label),
    },
    {
      label: "BENG1 score",
      value: formatValue(property.current_estimated_BENG1_score),
    },
    {
      label: "BENG2 score",
      value: formatValue(property.current_estimated_BENG2_score),
    },
    { label: "CO2 uitstoot", value: formatValue(property.CO2) },
  ];

  const sustainabilityInfo = [
    { label: "Installatie", value: formatValue(property.installation) },
    { label: "Muurisolatie", value: formatValue(property.wall_insulation) },
    { label: "Dakisolatie", value: formatValue(property.roof_insulation) },
    { label: "Vloerisolatie", value: formatValue(property.floor_insulation) },
    {
      label: "Woonkamer ramen",
      value: formatValue(property.living_room_windows),
    },
    { label: "Slaapkamer ramen", value: formatValue(property.bedroom_windows) },
    { label: "Douche", value: formatValue(property.shower) },
    { label: "Ventilatie", value: formatValue(property.ventilation) },
    { label: "Zonnepanelen (m²)", value: formatValue(property.solar_panels) },
    {
      label: "Zonnepaneel watt peak",
      value: formatValue(property.solarpanel_watt_peak),
    },
  ];

  return (
    <div className="max-w-6xl mx-auto p-8">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold text-gray-800 mb-2">
          EPC Analyse Resultaten
        </h2>
        <p className="text-gray-600">
          Geëxtraheerde data uit uw EPC certificaat
        </p>
      </div>

      <div className="grid gap-8 mb-12">
        <div className="bg-white p-8 rounded-xl shadow-lg">
          <h3 className="text-xl font-semibold text-primary mb-6 pb-2 border-b-2 border-gray-200">
            Basisinformatie
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {basicInfo.map((item, index) => (
              <div
                key={index}
                className="flex justify-between items-center p-3 bg-gray-50 rounded-lg border-l-4 border-primary"
              >
                <span className="font-semibold text-gray-800">
                  {item.label}:
                </span>
                <span className="text-gray-600 font-medium">{item.value}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white p-8 rounded-xl shadow-lg">
          <h3 className="text-xl font-semibold text-primary mb-6 pb-2 border-b-2 border-gray-200">
            Energie Informatie
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {energyInfo.map((item, index) => (
              <div
                key={index}
                className="flex justify-between items-center p-3 bg-gray-50 rounded-lg border-l-4 border-primary"
              >
                <span className="font-semibold text-gray-800">
                  {item.label}:
                </span>
                <span className="text-gray-600 font-medium">{item.value}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white p-8 rounded-xl shadow-lg">
          <h3 className="text-xl font-semibold text-primary mb-6 pb-2 border-b-2 border-gray-200">
            Verduurzaming Details
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sustainabilityInfo.map((item, index) => (
              <div
                key={index}
                className="flex justify-between items-center p-3 bg-gray-50 rounded-lg border-l-4 border-primary"
              >
                <span className="font-semibold text-gray-800">
                  {item.label}:
                </span>
                <span className="text-gray-600 font-medium">{item.value}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="flex justify-center my-8">
        <RedoButton modify={modifyResults} clear={clearResults} />
      </div>

      <div className="bg-green-50 p-8 rounded-xl border-l-4 border-primary">
        <h3 className="text-xl font-semibold text-primary mb-4">
          Volgende Stappen
        </h3>
        <p className="text-gray-800 leading-relaxed">
          Deze geëxtraheerde data kan gebruikt worden als input voor de
          Sustainability API om uitgebreide verduurzamingsadviezen te genereren.
        </p>
      </div>
    </div>
  );
};

export default EPCResult;
