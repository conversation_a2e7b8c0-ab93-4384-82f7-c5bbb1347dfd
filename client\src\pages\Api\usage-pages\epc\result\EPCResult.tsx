import React from "react";
import { useHistory } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../../../../redux/hooks";
import { clearEPCResults, modifyEPCQueries } from "../../../../../redux/actions/epcActions";
import { EPCResult as EPCResultType } from "../types";
import { Redirect } from "react-router-dom";
import RedoButton from "../../components/RedoButtons";

const EPCResult = () => {
  const { result, loading } = useAppSelector((state) => state.epc);
  const history = useHistory();
  const dispatch = useAppDispatch();

  const clearResults = () => {
    dispatch(clearEPCResults());
    history.push("/epc");
  };

  const modifyResults = () => {
    dispatch(modifyEPCQueries());
    history.push("/epc");
  };

  if (Object.keys(result).length === 0 && !loading) {
    return <Redirect to="/epc" />;
  }

  const property = result as EPCResultType;

  const formatValue = (value: any): string => {
    if (value === null || value === undefined) return "-";
    if (typeof value === "number") return value.toString();
    return value;
  };

  const basicInfo = [
    { label: "Postcode", value: formatValue(property.post_code) },
    { label: "Huisnummer", value: formatValue(property.house_number) },
    { label: "Huisnummer toevoeging", value: formatValue(property.house_addition) },
    { label: "Bouwjaar", value: formatValue(property.build_year) },
    { label: "Woningtype", value: formatValue(property.house_type) },
    { label: "Woonoppervlakte (m²)", value: formatValue(property.inner_surface_area) },
  ];

  const energyInfo = [
    { label: "Huidig energielabel", value: formatValue(property.current_estimated_energy_label) },
    { label: "Definitief energielabel", value: formatValue(property.definitive_energy_label) },
    { label: "BENG1 score", value: formatValue(property.current_estimated_BENG1_score) },
    { label: "BENG2 score", value: formatValue(property.current_estimated_BENG2_score) },
    { label: "CO2 uitstoot", value: formatValue(property.CO2) },
  ];

  const sustainabilityInfo = [
    { label: "Installatie", value: formatValue(property.installation) },
    { label: "Muurisolatie", value: formatValue(property.wall_insulation) },
    { label: "Dakisolatie", value: formatValue(property.roof_insulation) },
    { label: "Vloerisolatie", value: formatValue(property.floor_insulation) },
    { label: "Woonkamer ramen", value: formatValue(property.living_room_windows) },
    { label: "Slaapkamer ramen", value: formatValue(property.bedroom_windows) },
    { label: "Douche", value: formatValue(property.shower) },
    { label: "Ventilatie", value: formatValue(property.ventilation) },
    { label: "Zonnepanelen (m²)", value: formatValue(property.solar_panels) },
    { label: "Zonnepaneel watt peak", value: formatValue(property.solarpanel_watt_peak) },
  ];

  return (
    <div className="epc-result">
      <div className="result-header">
        <h2>EPC Analyse Resultaten</h2>
        <p>Geëxtraheerde data uit uw EPC certificaat</p>
      </div>

      <div className="result-sections">
        <div className="result-section">
          <h3>Basisinformatie</h3>
          <div className="result-grid">
            {basicInfo.map((item, index) => (
              <div key={index} className="result-item">
                <span className="label">{item.label}:</span>
                <span className="value">{item.value}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="result-section">
          <h3>Energie Informatie</h3>
          <div className="result-grid">
            {energyInfo.map((item, index) => (
              <div key={index} className="result-item">
                <span className="label">{item.label}:</span>
                <span className="value">{item.value}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="result-section">
          <h3>Verduurzaming Details</h3>
          <div className="result-grid">
            {sustainabilityInfo.map((item, index) => (
              <div key={index} className="result-item">
                <span className="label">{item.label}:</span>
                <span className="value">{item.value}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="result-actions">
        <RedoButton modify={modifyResults} clear={clearResults} />
      </div>

      <div className="next-steps">
        <h3>Volgende Stappen</h3>
        <p>
          Deze geëxtraheerde data kan gebruikt worden als input voor de Sustainability API 
          om uitgebreide verduurzamingsadviezen te genereren.
        </p>
      </div>
    </div>
  );
};

export default EPCResult;
